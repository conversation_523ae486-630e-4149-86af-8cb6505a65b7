using System.Text.Json;
using System.Text.Json.Serialization;

namespace GCP.Common
{
    /// <summary>
    /// 通用字符串转换器，能够将任何类型的JSON值转换为字符串
    /// 用于处理前端传来的 JSON 中可能包含数字、布尔值、对象、数组等类型的情况
    /// </summary>
    public class UniversalStringConverter : JsonConverter<string>
    {
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.TokenType switch
            {
                JsonTokenType.String => reader.GetString(),
                JsonTokenType.Number => reader.GetDecimal().ToString(),
                JsonTokenType.True => "true",
                JsonTokenType.False => "false",
                JsonTokenType.Null => null,
                JsonTokenType.StartObject => ReadObjectAsString(ref reader),
                JsonTokenType.StartArray => ReadArrayAsString(ref reader),
                _ => reader.GetString() // 默认尝试作为字符串读取
            };
        }

        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value);
        }

        private string ReadObjectAsString(ref Utf8JsonReader reader)
        {
            // 对于复杂对象，将其序列化为 JSON 字符串
            using var document = JsonDocument.ParseValue(ref reader);
            return document.RootElement.GetRawText();
        }

        private string ReadArrayAsString(ref Utf8JsonReader reader)
        {
            // 对于数组，将其序列化为 JSON 字符串
            using var document = JsonDocument.ParseValue(ref reader);
            return document.RootElement.GetRawText();
        }
    }
}
