﻿using System.Text.Json;

namespace GCP.Common
{
    /// <summary>
    /// 转换帮助类
    /// </summary>
    public static class ConvertHelper
    {
        static Dictionary<string, Type> typeMapping = new Dictionary<string, Type>
        {
            { "string", typeof(string) },
            { "decimal", typeof(decimal) },
            { "int", typeof(int) },
            { "short", typeof(short) },
            { "long", typeof(long) },
            { "DateTime", typeof(DateTime) },
            { "bool", typeof(bool) },
            { "char", typeof(char) },
            { "double", typeof(double) },
            { "float", typeof(float) },
            { "byte[]", typeof(byte[]) }
        };

        public static JsonSerializerOptions JsonSerializerOptions { get; set; }

        public static object Parse(this object value, string typeName)
        {
            if(!typeMapping.TryGetValue(typeName, out Type type))
            {
                return value;
            }
            var result = ChangeType(value, type);
            return result;
        }

        /// <summary>
        /// object转换成任意类型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <param name="defaultVal"></param>
        /// <returns></returns>
        public static T Parse<T>(this object value, T defaultVal = default(T))
        {
            var type = typeof(T);
            var result = ChangeType(value, type);
            if (result == null) return defaultVal;
            return (T)result;
        }

        /// <summary>
        /// 任意类型转换
        /// </summary>
        public static object ChangeType(object value, Type type)
        {
            // 如果值为空或DBNull，直接返回默认值
            if (value == null || (value is string str && string.IsNullOrEmpty(str)) || Convert.IsDBNull(value))
            {
                // 对于可空类型，返回 null 是正确的默认值
                // 对于非可空的值类型，无法返回 null，因此返回该类型的默认值（例如 int 的 0）
                // 对于引用类型，返回 null
                return type.IsValueType ? Activator.CreateInstance(type) : null;
            }

            // 如果目标类型是泛型并且是 Nullable<T>
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                // 获取 Nullable<T> 的基础类型 T
                var underlyingType = Nullable.GetUnderlyingType(type);
                // 递归调用 ChangeType，将值转换为基础类型
                return ChangeType(value, underlyingType);
            }

            if (type == value.GetType()) return value;

            if (type.IsEnum)
            {
                if (value is string enumName)
                    return Enum.Parse(type, enumName);
                else
                    return Enum.ToObject(type, value);
            }

            // 不常用（隐藏）
            // if (type == typeof(Guid) && value is string guid) return new Guid(guid);
            // if (type == typeof(Version) && value is string ver) return new Version(ver);

            if (value is JsonElement json)
            {
                if (type == typeMapping["string"])
                {
                    // 对于字符串类型，根据JSON元素的类型进行适当的转换
                    return json.ValueKind switch
                    {
                        JsonValueKind.String => json.GetString(),
                        JsonValueKind.Number => json.GetDecimal().ToString(),
                        JsonValueKind.True => "true",
                        JsonValueKind.False => "false",
                        JsonValueKind.Null => null,
                        JsonValueKind.Object => json.GetRawText(), // 对象转为JSON字符串
                        JsonValueKind.Array => json.GetRawText(),  // 数组转为JSON字符串
                        _ => json.ToString()
                    };
                }
                return json.Deserialize(type, JsonSerializerOptions);
                //return JsonSerializer.Deserialize(json.GetRawText(), type); // 更通用的方式
            }

            // 如果值实现了IConvertible，使用Convert.ChangeType进行转换
            if (value is IConvertible)
            {
                return Convert.ChangeType(value, type);
            }

            // 如果以上转换都不适用，尝试返回原值
            return value;
        }

        public static object ChangeTypeIfLong(object value, Type type)
        {
            if(type == typeMapping["long"])
            {
                return value.ToString();
            }
            return ChangeType(value, type);
        }
    }
}
