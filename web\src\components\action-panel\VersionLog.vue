<template>
  <CmpContainer full>
    <CmpCard>
      <t-tabs v-model="activeTab" default-value="history">
        <t-tab-panel value="history" label="版本历史">
          <div class="version-history-panel">
            <t-table
              :data="versionLogs"
              :columns="columns"
              :loading="loading"
              row-key="id"
              hover
              stripe
              :pagination="pagination"
              @page-change="onPageChange"
            >
              <template #version="{ row }">
                <t-tag :theme="row.isCurrentVersion ? 'success' : 'primary'" variant="light">
                  v{{ row.version }}
                  <span v-if="row.isCurrentVersion"> (当前)</span>
                </t-tag>
              </template>
              <template #operation="{ row }">
                <t-space>
                  <t-button
                    v-if="row.version !== currentVersion"
                    theme="primary"
                    variant="text"
                    size="small"
                    @click="onCompareVersion(row)"
                  >
                    对比
                  </t-button>
                  <t-button
                    v-if="row.version !== currentVersion"
                    theme="warning"
                    variant="text"
                    size="small"
                    @click="onRollbackVersion(row)"
                  >
                    回滚
                  </t-button>
                  <t-button
                    v-if="!row.isCurrentVersion"
                    theme="success"
                    variant="text"
                    size="small"
                    @click="onPublishVersion(row)"
                  >
                    发布
                  </t-button>
                </t-space>
              </template>
            </t-table>
          </div>
        </t-tab-panel>
        <t-tab-panel value="compare" label="版本对比">
          <div class="version-compare-panel">
            <div class="compare-header">
              <t-space>
                <t-select v-model="compareVersion1" :options="versionOptions" placeholder="选择版本1"></t-select>
                <span>VS</span>
                <t-select v-model="compareVersion2" :options="versionOptions" placeholder="选择版本2"></t-select>
                <t-button theme="primary" @click="compareVersions">对比</t-button>
              </t-space>
            </div>
            <div v-if="compareResult" class="compare-result">
              <t-divider>对比结果</t-divider>
              <div class="compare-summary">
                <t-alert
                  :theme="compareResult.hasChanges ? 'warning' : 'success'"
                  :message="compareResult.hasChanges ? '版本内容有差异' : '版本内容相同'"
                ></t-alert>
              </div>
              <div v-if="compareResult.hasChanges" class="compare-diff">
                <div class="diff-editor-container">
                  <vue-monaco-diff-editor
                    v-model:original="compareResult.code1"
                    v-model:modified="compareResult.code2"
                    language="json"
                    :options="diffEditorOptions"
                    height="500px"
                  />
                </div>
              </div>
              <div v-else class="no-diff">
                <t-empty description="两个版本内容完全相同" />
              </div>
            </div>
          </div>
        </t-tab-panel>
      </t-tabs>

      <!-- 回滚确认对话框 -->
      <t-dialog
        v-model:visible="rollbackDialogVisible"
        header="回滚确认"
        :confirm-btn="{ content: '确认回滚', theme: 'danger' }"
        :cancel-btn="{ content: '取消' }"
        @confirm="confirmRollback"
      >
        <template #body>
          <p>确定要回滚到版本 v{{ rollbackVersion }}？</p>
          <t-input v-model="rollbackRemark" placeholder="请输入回滚原因（可选）" style="margin-top: 16px"></t-input>
        </template>
      </t-dialog>

      <!-- 发布确认对话框 -->
      <t-dialog
        v-model:visible="publishDialogVisible"
        header="发布确认"
        :confirm-btn="{ content: '确认发布', theme: 'primary' }"
        :cancel-btn="{ content: '取消' }"
        @confirm="confirmPublish"
      >
        <template #body>
          <p>确定要发布版本 v{{ publishVersion }}？</p>
          <t-input v-model="publishRemark" placeholder="请输入发布说明（可选）" style="margin-top: 16px"></t-input>
        </template>
      </t-dialog> </CmpCard
  ></CmpContainer>
</template>

<script lang="ts">
export default {
  name: 'VersionLog',
  components: {
    VueMonacoDiffEditor,
  },
};
</script>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import { VueMonacoDiffEditor } from '@guolao/vue-monaco-editor';

const props = defineProps<{
  functionId: string;
}>();

// Monaco Editor 配置
const diffEditorOptions = {
  readOnly: true,
  automaticLayout: true,
  scrollBeyondLastLine: false,
  minimap: { enabled: false },
  wordWrap: 'on' as const,
  renderSideBySide: true,
  ignoreTrimWhitespace: false,
  renderWhitespace: 'boundary' as const,
  diffWordWrap: 'on' as const,
};

// 版本日志数据
const versionLogs = ref<any[]>([]);
const loading = ref(false);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 当前版本
const currentVersion = ref<number>(0);
const activeTab = ref('history');

// 表格列定义
const columns = [
  { colKey: 'version', title: '版本号', width: 120 },
  { colKey: 'creator', title: '创建人', width: 120 },
  { colKey: 'timeCreate', title: '创建时间', width: 180 },
  { colKey: 'operation', title: '操作', width: 200 },
];

// 版本对比
const compareVersion1 = ref<number>();
const compareVersion2 = ref<number>();
const compareResult = ref<any>(null);

// 版本选项
const versionOptions = computed(() => {
  return versionLogs.value.map((log) => ({
    label: `v${log.version} (${log.timeCreate})`,
    value: log.version,
  }));
});

// 回滚相关
const rollbackDialogVisible = ref(false);
const rollbackVersion = ref<number>(0);
const rollbackRemark = ref('');

// 发布相关
const publishDialogVisible = ref(false);
const publishVersion = ref<number>(0);
const publishRemark = ref('');

// 加载版本日志
const loadVersionLogs = async () => {
  if (!props.functionId) {
    console.warn('functionId 为空，跳过加载版本日志');
    return;
  }

  loading.value = true;
  try {
    console.log('开始加载版本日志:', props.functionId);

    const result = await api.run(Services.functionCodeGetVersionLogs, {
      funcId: props.functionId,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });
    versionLogs.value = result || [];

    console.log('版本日志加载完成:', versionLogs.value);

    // 获取当前最新版本
    const latestVersion = await api.run(Services.functionCodeGetLatestVersion, {
      funcId: props.functionId,
    });
    currentVersion.value = latestVersion || 0;

    console.log('当前最新版本:', currentVersion.value);
  } catch (error) {
    console.error('加载版本日志失败:', error);
    MessagePlugin.error('加载版本日志失败：' + (error?.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  loadVersionLogs();
};

// 对比版本
const compareVersions = async () => {
  if (!compareVersion1.value || !compareVersion2.value) {
    MessagePlugin.warning('请选择要对比的两个版本');
    return;
  }

  if (!props.functionId) {
    MessagePlugin.warning('函数ID不能为空');
    return;
  }

  try {
    console.log('开始对比版本:', {
      functionId: props.functionId,
      version1: compareVersion1.value,
      version2: compareVersion2.value,
    });

    // 获取两个版本的代码
    const [code1, code2] = await Promise.all([
      api.run(Services.functionCodeGetCodeByVersion, {
        funcId: props.functionId,
        version: compareVersion1.value,
      }),
      api.run(Services.functionCodeGetCodeByVersion, {
        funcId: props.functionId,
        version: compareVersion2.value,
      }),
    ]);

    console.log('获取到的代码:', { code1, code2 });

    // 格式化JSON代码
    const formattedCode1 = formatJson(code1 || '{}');
    const formattedCode2 = formatJson(code2 || '{}');

    // 检查是否有差异
    const hasChanges = formattedCode1 !== formattedCode2;

    compareResult.value = {
      version1: compareVersion1.value,
      version2: compareVersion2.value,
      hasChanges,
      code1: formattedCode1,
      code2: formattedCode2,
    };

    activeTab.value = 'compare';
    console.log('对比结果:', compareResult.value);

    MessagePlugin.success('版本对比完成');
  } catch (error) {
    console.error('对比版本失败:', error);
    MessagePlugin.error('对比版本失败：' + (error?.message || '未知错误'));
  }
};

// 点击对比按钮
const onCompareVersion = async (row: any) => {
  compareVersion1.value = row.version;

  // 获取当前使用版本
  try {
    const currentUseVersion = await api.run(Services.functionCodeGetCurrentUseVersion, {
      funcId: props.functionId,
    });
    compareVersion2.value = currentUseVersion || currentVersion.value;
    compareVersions();
  } catch (error) {
    MessagePlugin.error('获取当前使用版本失败：' + error.message);
  }
};

// 点击回滚按钮
const onRollbackVersion = (row: any) => {
  rollbackVersion.value = row.version;
  rollbackDialogVisible.value = true;
};

// 确认回滚
const confirmRollback = async () => {
  try {
    const result = await api.run(Services.functionCodeRollbackToVersion, {
      funcId: props.functionId,
      targetVersion: rollbackVersion.value,
      remark: rollbackRemark.value,
    });

    if (result.success) {
      MessagePlugin.success(result.message);
      loadVersionLogs();
    } else {
      MessagePlugin.warning(result.message);
    }
  } catch (error) {
    MessagePlugin.error('回滚失败：' + error.message);
  } finally {
    rollbackDialogVisible.value = false;
    rollbackRemark.value = '';
  }
};

// 点击发布按钮
const onPublishVersion = (row: any) => {
  publishVersion.value = row.version;
  publishDialogVisible.value = true;
};

// 确认发布
const confirmPublish = async () => {
  try {
    const result = await api.run(Services.functionCodePublishVersion, {
      funcId: props.functionId,
      version: publishVersion.value,
      remark: publishRemark.value,
    });

    if (result.success) {
      MessagePlugin.success(result.message);
      loadVersionLogs();
    } else {
      MessagePlugin.warning(result.message);
    }
  } catch (error) {
    MessagePlugin.error('发布失败：' + error.message);
  } finally {
    publishDialogVisible.value = false;
    publishRemark.value = '';
  }
};

// 格式化JSON
const formatJson = (json: string) => {
  try {
    return JSON.stringify(JSON.parse(json), null, 2);
  } catch {
    return json;
  }
};

// 监听 functionId 变化，重新加载数据
watch(
  () => props.functionId,
  (newFunctionId) => {
    if (newFunctionId) {
      // 重置状态
      compareResult.value = null;
      compareVersion1.value = undefined;
      compareVersion2.value = undefined;
      // 重新加载数据
      loadVersionLogs();
    }
  },
  { immediate: true },
);

// 暴露刷新方法给父组件
const refresh = () => {
  loadVersionLogs();
};

defineExpose({
  refresh,
});

onMounted(() => {
  loadVersionLogs();
});
</script>

<style lang="less" scoped>
.version-history-panel {
  margin-top: 16px;
}

.version-compare-panel {
  margin-top: 16px;

  .compare-header {
    margin-bottom: 16px;
  }

  .compare-result {
    margin-top: 16px;

    .compare-summary {
      margin-bottom: 16px;
    }

    .compare-diff {
      margin-top: 16px;

      .diff-editor-container {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
      }
    }

    .no-diff {
      margin-top: 16px;
      padding: 40px;
      text-align: center;
    }
  }
}
</style>
